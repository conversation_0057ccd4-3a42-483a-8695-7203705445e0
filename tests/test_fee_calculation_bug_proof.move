#[test_only]
module haedal::test_fee_calculation_bug_proof {
    use sui::test_utils::{assert_eq};
    use std::debug;
    use std::vector;

    // Constants from staking.move - these are the exact values used in the vulnerable code
    const DEFAULT_SERVICE_FEE_RATE: u64 = 90_0000;  // 0.9%
    const FEE_DENOMINATOR: u64 = 1000_0000;         // 10,000,000

    /// MATHEMATICAL PROOF: Fee calculation bug allows zero fees
    /// 
    /// This test provides 100% mathematical proof that the bug exists by demonstrating
    /// that the fee calculation formula: fee_amount = (amount * service_fee) / FEE_DENOMINATOR
    /// results in zero for small amounts due to integer division truncation.
    #[test]
    fun test_mathematical_proof_of_fee_calculation_bug() {
        debug::print(&b"=== MATHEMATICAL PROOF OF FEE CALCULATION BUG ===");
        
        // Use the exact same constants and formula as in staking.move line 367
        let service_fee_rate = DEFAULT_SERVICE_FEE_RATE; // 90_0000 (0.9%)
        let fee_denominator = FEE_DENOMINATOR;           // 1000_0000
        
        debug::print(&b"Service fee rate:");
        debug::print(&service_fee_rate);
        debug::print(&b"Fee denominator:");
        debug::print(&fee_denominator);
        
        // Calculate the threshold: amounts below this result in zero fee
        let threshold = fee_denominator / service_fee_rate;
        debug::print(&b"Threshold amount (amounts below this = zero fee):");
        debug::print(&threshold);
        debug::print(&b"Threshold calculation: FEE_DENOMINATOR / service_fee_rate =");
        debug::print(&(1000_0000 / 90_0000));
        
        // Test multiple amounts that should result in zero fee
        let test_amounts = vector[1, 5, 10, 11];
        let i = 0;
        
        debug::print(&b"=== TESTING VULNERABLE AMOUNTS ===");
        while (i < 4) {
            let amount = *vector::borrow(&test_amounts, i);
            
            // This is the EXACT formula from staking.move line 367:
            // let fee_amount = ((max_exchange_sui_amount as u128) * (service_fee as u128) / (FEE_DENOMINATOR as u128) as u64);
            let calculated_fee = ((amount as u128) * (service_fee_rate as u128) / (fee_denominator as u128) as u64);
            
            debug::print(&b"--- Test Case ---");
            debug::print(&b"Amount:");
            debug::print(&amount);
            debug::print(&b"Calculation: (amount * service_fee_rate) / fee_denominator");
            debug::print(&((amount as u128) * (service_fee_rate as u128)));
            debug::print(&b"Divided by fee_denominator:");
            debug::print(&fee_denominator);
            debug::print(&b"Result (fee_amount):");
            debug::print(&calculated_fee);
            
            // CRITICAL ASSERTION: All these amounts result in ZERO fee
            assert_eq(calculated_fee, 0);
            debug::print(&b"CONFIRMED: Fee is ZERO despite non-zero service fee rate!");
            
            i = i + 1;
        };
        
        // Test the boundary case - amount that just starts paying fee
        let boundary_amount = threshold; // This should be the first amount that pays fee
        let boundary_fee = ((boundary_amount as u128) * (service_fee_rate as u128) / (fee_denominator as u128) as u64);
        
        debug::print(&b"=== BOUNDARY TEST ===");
        debug::print(&b"Boundary amount (should pay fee):");
        debug::print(&boundary_amount);
        debug::print(&b"Boundary fee:");
        debug::print(&boundary_fee);
        
        // The boundary amount should pay at least 1 unit of fee
        if (boundary_fee == 0) {
            debug::print(&b"WARNING: Even boundary amount pays zero fee!");
        };
        
        debug::print(&b"=== CONCLUSION ===");
        debug::print(&b"BUG MATHEMATICALLY PROVEN:");
        debug::print(&b"1. Service fee rate is configured (0.9%)");
        debug::print(&b"2. Multiple amounts (1, 5, 10, 11) result in ZERO fee");
        debug::print(&b"3. This allows users to bypass service fees entirely");
        debug::print(&b"4. The bug exists in the integer division truncation");
        debug::print(&b"ROOT CAUSE: (small_amount * 90_0000) / 1000_0000 < 1, truncates to 0");
    }

    /// Test demonstrating the exact vulnerable calculation from staking.move
    #[test]
    fun test_exact_vulnerable_calculation_from_staking_move() {
        debug::print(&b"=== EXACT REPLICATION OF STAKING.MOVE CALCULATION ===");
        
        // These are the exact lines from staking.move:366-367
        let service_fee = DEFAULT_SERVICE_FEE_RATE; // config::get_service_fee(&staking.config)
        let max_exchange_sui_amount = 11u64; // Small amount that triggers the bug
        
        // This is line 367 from staking.move - EXACT COPY:
        let fee_amount = ((max_exchange_sui_amount as u128) * (service_fee as u128) / (FEE_DENOMINATOR as u128) as u64);
        
        debug::print(&b"Replicating staking.move line 367:");
        debug::print(&b"max_exchange_sui_amount:");
        debug::print(&max_exchange_sui_amount);
        debug::print(&b"service_fee:");
        debug::print(&service_fee);
        debug::print(&b"FEE_DENOMINATOR:");
        debug::print(&FEE_DENOMINATOR);
        debug::print(&b"fee_amount result:");
        debug::print(&fee_amount);
        
        // The commented out assertion from line 369 would catch this:
        // assert!((service_fee == 0 || fee_amount > 0), EUnstakeInstantNoServiceFee);
        
        debug::print(&b"Checking the commented assertion condition:");
        debug::print(&b"service_fee == 0:");
        debug::print(&(service_fee == 0));
        debug::print(&b"fee_amount > 0:");
        debug::print(&(fee_amount > 0));
        debug::print(&b"Assertion would be: (service_fee == 0 || fee_amount > 0)");
        debug::print(&((service_fee == 0) || (fee_amount > 0)));
        
        // PROOF: The assertion would FAIL because service_fee != 0 but fee_amount == 0
        assert_eq(service_fee == 0, false); // Service fee is configured
        assert_eq(fee_amount > 0, false);   // But calculated fee is zero
        assert_eq(fee_amount, 0);           // Fee is exactly zero
        
        debug::print(&b"SMOKING GUN EVIDENCE:");
        debug::print(&b"- Service fee is configured (non-zero)");
        debug::print(&b"- But calculated fee is zero");
        debug::print(&b"- The commented assertion would FAIL");
        debug::print(&b"- This proves users can bypass fees!");
    }
}
