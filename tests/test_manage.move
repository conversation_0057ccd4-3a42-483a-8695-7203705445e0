#[test_only]
module haedal::test_manage {
    use sui::clock;
    use sui::coin;
    use sui::test_scenario::{<PERSON>, <PERSON><PERSON><PERSON>};
    use haedal::hasui;
    use haedal::staking;
    use haedal::manage;
    use haedal::config;
    use haedal::haedal_test;
    use sui_system::sui_system::{Self, SuiSystemState};
    use sui_system::governance_test_utils::{
        Self,
        add_validator,
        add_validator_candidate,
        advance_epoch,
        advance_epoch_with_reward_amounts,
        create_validator_for_testing,
        create_sui_system_state_for_testing,
        stake_with,
        remove_validator,
        remove_validator_candidate,
        total_sui_balance,
        unstake,
    };
    use std::vector;
    use sui::sui::SUI;

    const TEST_DEPLOYER: address = @0x7869616f70656e67;
    const TEST_STAKER_1: address = @0x1234;
    const INACTIVE_VALIDATOR: address = @0x9999;

    #[test]
    fun test_configs() {
        // setup sui system
        haedal_test::set_up_sui_system_state_with_storage_fund();
        
        // setup the env
        let (scenario_object, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_object;

        // test config setters
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        {
            manage::set_deposit_fee(&admin_cap, &mut staking_object, 100);
            assert!(100 == config::get_deposit_fee(staking::get_config_mut(&mut staking_object)), 2);
            
            manage::set_reward_fee(&admin_cap, &mut staking_object, 200);
            assert!(200 == config::get_reward_fee(staking::get_config_mut(&mut staking_object)), 3);
            
            manage::set_validator_reward_fee(&admin_cap, &mut staking_object, 300);
            assert!(300 == config::get_validator_reward_fee(staking::get_config_mut(&mut staking_object)), 4);
            
            manage::set_service_fee(&admin_cap, &mut staking_object, 400);
            assert!(400 == config::get_service_fee(staking::get_config_mut(&mut staking_object)), 5);

            manage::set_withdraw_time_limit(&admin_cap, &mut staking_object, 22*3600*1000);
            assert!(22*3600*1000 == config::get_withdraw_time_limit(staking::get_config_mut(&mut staking_object)), 6);

            manage::set_validator_count(&admin_cap, &mut staking_object, 20);
            assert!(20 == config::get_validator_count(staking::get_config_mut(&mut staking_object)), 7);

            assert!(4 == staking::get_version(&staking_object), 8);

            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
            manage::collect_rewards_fee_v2(&admin_cap, &mut system_state, &mut staking_object, TEST_DEPLOYER, test_scenario::ctx(scenario));
            test_scenario::return_shared(system_state);

            manage::collect_service_fee(&admin_cap, &mut staking_object, TEST_DEPLOYER, test_scenario::ctx(scenario));

            let pause_claim = false;
            assert!(pause_claim == staking::query_pause_claim(&staking_object), 9);
            
            manage::toggle_claim(&admin_cap, &mut staking_object, pause_claim);
            assert!(pause_claim == staking::query_pause_claim(&staking_object), 10);

            pause_claim = true;
            manage::toggle_claim(&admin_cap, &mut staking_object, pause_claim);
            assert!(pause_claim == staking::query_pause_claim(&staking_object), 11);
        };
        
        haedal_test::haedal_test_tear_down(scenario_object, staking_object, admin_cap, clock_object);
    }




}
