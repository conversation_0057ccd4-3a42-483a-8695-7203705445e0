#[test_only]
module haedal::test_validator_validation_poc {
    use sui::clock;
    use sui::coin;
    use sui::test_scenario::{<PERSON>, <PERSON><PERSON><PERSON>};
    use haedal::hasui;
    use haedal::staking;
    use haedal::manage;
    use haedal::config;
    use haedal::haedal_test;
    use sui_system::sui_system::{Self, SuiSystemState};
    use sui_system::governance_test_utils::{
        Self,
        add_validator,
        add_validator_candidate,
        advance_epoch,
        advance_epoch_with_reward_amounts,
        create_validator_for_testing,
        create_sui_system_state_for_testing,
        stake_with,
        remove_validator,
        remove_validator_candidate,
        total_sui_balance,
        unstake,
    };
    use std::vector;
    use sui::sui::SUI;

    const TEST_DEPLOYER: address = @0x7869616f70656e67;
    const TEST_STAKER_1: address = @0x1234;
    const INACTIVE_VALIDATOR_1: address = @0x9999;
    const INACTIVE_VALIDATOR_2: address = @0x8888;
    const INACTIVE_VALIDATOR_3: address = @0x7777;

    /// POC Test 1: Demonstrates that do_stake fails when provided with a single inactive validator
    /// This proves the vulnerability exists - the function doesn't validate that provided validators are active
    #[test]
    #[expected_failure(abort_code = sui_system::validator_set::ENotAValidator)]
    fun poc_do_stake_single_inactive_validator_causes_revert() {
        // Setup sui system with 2 active validators (@0x1 and @0x2)
        haedal_test::set_up_sui_system_state_with_storage_fund();
        
        // Setup the haedal staking system
        let (scenario_object, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_object;

        // Add SUI to the vault by having a user stake
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        {
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
            let sui_coin = coin::mint_for_testing<SUI>(1000000000, test_scenario::ctx(scenario)); // 1 SUI

            // User stakes SUI with validator selection @0x0 (protocol selects) - this adds SUI to vault
            let hasui_coin = staking::request_stake_coin(&mut system_state, &mut staking_object, sui_coin, @0x0, test_scenario::ctx(scenario));
            coin::burn_for_testing(hasui_coin); // Dispose of the returned coin

            test_scenario::return_shared(system_state);
        };

        // Attempt to call do_stake with an inactive validator - this should fail
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        {
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);

            // Verify our test setup: get active validators and confirm INACTIVE_VALIDATOR_1 is not active
            let active_validators = sui_system::active_validator_addresses(&mut system_state);
            assert!(!vector::contains(&active_validators, &INACTIVE_VALIDATOR_1), 1);
            
            // Create validator list with only the inactive validator
            let faulty_validators = vector[INACTIVE_VALIDATOR_1];
            
            // BUG: do_stake computes active_validators but doesn't verify the provided validators are all active
            // This call will fail when stake_to_validator calls sui_system::request_add_stake_non_entry
            // with an inactive validator address
            manage::do_stake(&admin_cap, &mut staking_object, &mut system_state, faulty_validators, test_scenario::ctx(scenario));
            
            test_scenario::return_shared(system_state);
        };

        haedal_test::haedal_test_tear_down(scenario_object, staking_object, admin_cap, clock_object);
    }

    /// POC Test 2: Demonstrates that do_stake fails even with mixed active/inactive validators
    /// This shows that a single inactive validator in the list can cause the entire operation to fail
    #[test]
    #[expected_failure(abort_code = sui_system::validator_set::ENotAValidator)]
    fun poc_do_stake_mixed_validators_fails_on_inactive() {
        // Setup sui system with 2 active validators
        haedal_test::set_up_sui_system_state_with_storage_fund();
        
        // Setup the haedal staking system
        let (scenario_object, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_object;

        // Add sufficient SUI to the vault for multiple validator stakes
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        {
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
            let sui_coin = coin::mint_for_testing<SUI>(3000000000, test_scenario::ctx(scenario)); // 3 SUI

            let hasui_coin = staking::request_stake_coin(&mut system_state, &mut staking_object, sui_coin, @0x0, test_scenario::ctx(scenario));
            coin::burn_for_testing(hasui_coin); // Dispose of the returned coin

            test_scenario::return_shared(system_state);
        };

        // Attempt to call do_stake with mixed active and inactive validators
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        {
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);

            // Get an active validator
            let active_validators = sui_system::active_validator_addresses(&mut system_state);
            let active_validator = *vector::borrow(&active_validators, 0);

            // Verify our inactive validator is not active
            assert!(!vector::contains(&active_validators, &INACTIVE_VALIDATOR_2), 1);
            
            // Create validator list with both active and inactive validators
            let mixed_validators = vector[active_validator, INACTIVE_VALIDATOR_2];
            
            // BUG: This will fail when it reaches the inactive validator
            // Even though the first validator is active, the second one causes the entire operation to fail
            manage::do_stake(&admin_cap, &mut staking_object, &mut system_state, mixed_validators, test_scenario::ctx(scenario));
            
            test_scenario::return_shared(system_state);
        };

        haedal_test::haedal_test_tear_down(scenario_object, staking_object, admin_cap, clock_object);
    }

    /// POC Test 3: Demonstrates successful staking with only active validators (control test)
    /// This proves that the function works correctly when all validators are active
    #[test]
    fun poc_do_stake_with_active_validators_succeeds() {
        // Setup sui system with 2 active validators
        haedal_test::set_up_sui_system_state_with_storage_fund();
        
        // Setup the haedal staking system
        let (scenario_object, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_object;

        // Add SUI to the vault
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        {
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
            let sui_coin = coin::mint_for_testing<SUI>(2000000000, test_scenario::ctx(scenario)); // 2 SUI

            let hasui_coin = staking::request_stake_coin(&mut system_state, &mut staking_object, sui_coin, @0x0, test_scenario::ctx(scenario));
            coin::burn_for_testing(hasui_coin); // Dispose of the returned coin

            test_scenario::return_shared(system_state);
        };

        // Call do_stake with only active validators - this should succeed
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        {
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);

            // Get active validators
            let active_validators = sui_system::active_validator_addresses(&mut system_state);
            
            // Use only active validators
            let valid_validators = vector[
                *vector::borrow(&active_validators, 0),
                *vector::borrow(&active_validators, 1)
            ];
            
            // This should succeed because all validators are active
            manage::do_stake(&admin_cap, &mut staking_object, &mut system_state, valid_validators, test_scenario::ctx(scenario));
            
            test_scenario::return_shared(system_state);
        };

        haedal_test::haedal_test_tear_down(scenario_object, staking_object, admin_cap, clock_object);
    }

    /// POC Test 4: Demonstrates the vulnerability with multiple inactive validators
    #[test]
    #[expected_failure(abort_code = sui_system::validator_set::ENotAValidator)]
    fun poc_do_stake_multiple_inactive_validators() {
        // Setup sui system
        haedal_test::set_up_sui_system_state_with_storage_fund();
        
        // Setup the haedal staking system
        let (scenario_object, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_object;

        // Add SUI to the vault
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        {
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
            let sui_coin = coin::mint_for_testing<SUI>(5000000000, test_scenario::ctx(scenario)); // 5 SUI

            let hasui_coin = staking::request_stake_coin(&mut system_state, &mut staking_object, sui_coin, @0x0, test_scenario::ctx(scenario));
            coin::burn_for_testing(hasui_coin); // Dispose of the returned coin

            test_scenario::return_shared(system_state);
        };

        // Attempt to call do_stake with multiple inactive validators
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        {
            let system_state = test_scenario::take_shared<SuiSystemState>(scenario);

            // Verify all our test validators are inactive
            let active_validators = sui_system::active_validator_addresses(&mut system_state);
            assert!(!vector::contains(&active_validators, &INACTIVE_VALIDATOR_1), 1);
            assert!(!vector::contains(&active_validators, &INACTIVE_VALIDATOR_2), 2);
            assert!(!vector::contains(&active_validators, &INACTIVE_VALIDATOR_3), 3);
            
            // Create validator list with multiple inactive validators
            let all_inactive_validators = vector[INACTIVE_VALIDATOR_1, INACTIVE_VALIDATOR_2, INACTIVE_VALIDATOR_3];
            
            // This will fail on the first inactive validator
            manage::do_stake(&admin_cap, &mut staking_object, &mut system_state, all_inactive_validators, test_scenario::ctx(scenario));
            
            test_scenario::return_shared(system_state);
        };

        haedal_test::haedal_test_tear_down(scenario_object, staking_object, admin_cap, clock_object);
    }
}
