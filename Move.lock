# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "CC5DEDECC770BA70A3B8D5D16419BBF5F67ECD12D7875E3ED36024C7E285ED64"
deps_digest = "3C4103934B1E040BB6B23F1D610B4EF9F2F1166A50A104EADCF77467C004C600"
dependencies = [
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "mainnet", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "mainnet", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "SuiSystem"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "mainnet", subdir = "crates/sui-framework/packages/sui-system" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
]

[move.toolchain-version]
compiler-version = "1.51.0"
edition = "2024"
flavor = "sui"
