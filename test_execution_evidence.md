# Test Execution Evidence

## Test 1: Mathematical Proof
**Command**: `sui move test test_mathematical_proof_of_fee_calculation_bug`

**Key Debug Output**:
```
[debug] === MATHEMATICAL PROOF OF FEE CALCULATION BUG ===
[debug] Service fee rate: 900000
[debug] Fee denominator: 10000000
[debug] Threshold amount (amounts below this = zero fee): 11

[debug] --- Test Case ---
[debug] Amount: 1
[debug] Result (fee_amount): 0
[debug] CONFIRMED: Fee is ZERO despite non-zero service fee rate!

[debug] --- Test Case ---
[debug] Amount: 5
[debug] Result (fee_amount): 0
[debug] CONFIRMED: Fee is ZERO despite non-zero service fee rate!

[debug] --- Test Case ---
[debug] Amount: 10
[debug] Result (fee_amount): 0
[debug] CONFIRMED: Fee is ZERO despite non-zero service fee rate!

[debug] --- Test Case ---
[debug] Amount: 11
[debug] Result (fee_amount): 0
[debug] CONFIRMED: Fee is ZERO despite non-zero service fee rate!

[debug] BUG MATHEMATICALLY PROVEN:
[debug] 1. Service fee rate is configured (0.9%)
[debug] 2. Multiple amounts (1, 5, 10, 11) result in ZERO fee
[debug] 3. This allows users to bypass service fees entirely
[debug] ROOT CAUSE: (small_amount * 90_0000) / 1000_0000 < 1, truncates to 0

[ PASS ] test_mathematical_proof_of_fee_calculation_bug
Test result: OK. Total tests: 1; passed: 1; failed: 0
```

## Test 2: Exact Code Replication
**Command**: `sui move test test_exact_vulnerable_calculation_from_staking_move`

**Key Debug Output**:
```
[debug] === EXACT REPLICATION OF STAKING.MOVE CALCULATION ===
[debug] Replicating staking.move line 367:
[debug] max_exchange_sui_amount: 11
[debug] service_fee: 900000
[debug] FEE_DENOMINATOR: 10000000
[debug] fee_amount result: 0

[debug] Checking the commented assertion condition:
[debug] service_fee == 0: false
[debug] fee_amount > 0: false
[debug] Assertion would be: (service_fee == 0 || fee_amount > 0): false

[debug] SMOKING GUN EVIDENCE:
[debug] - Service fee is configured (non-zero)
[debug] - But calculated fee is zero
[debug] - The commented assertion would FAIL
[debug] - This proves users can bypass fees!

[ PASS ] test_exact_vulnerable_calculation_from_staking_move
Test result: OK. Total tests: 1; passed: 1; failed: 0
```

## Conclusion
Both tests pass successfully, providing mathematical and code-based proof that:
1. Service fees are configured but can be bypassed
2. The vulnerability exists in the exact code location identified
3. The protective assertion would catch this but is commented out
4. Users can exploit this to avoid paying fees
